<template>
  <div class="badge" :class="[props.variant, props.size]">
    <div class="dot" v-if="props.dot"></div>
    <Icon v-if="props.icon" :name="props.icon" class="icon" size="small" />
    <div class="label" v-if="props.label">{{ props.label }}</div>
  </div>
</template>

<script setup lang="ts">
import { IconName } from '../../values/icons.const';
import Icon from '../icon.vue';
import { BadgeVariant } from './badge-variant.type';

interface BadgeProps {
  label?: string;
  variant?: BadgeVariant;
  dot?: boolean;
  size?: 'small' | 'medium';
  icon?: IconName;
}

const props = withDefaults(defineProps<BadgeProps>(), {
  variant: 'ghost',
  dot: false,
  size: 'medium',
});
</script>

<style scoped lang="scss">
.badge {
  display: flex;
  align-items: center;
  gap: var(--t-spacing-xs);

  padding: var(--t-spacing-xs) var(--t-spacing-sm);

  width: fit-content;
  max-height: fit-content;

  border-radius: var(--t-border-radius-xs);

  // Default colors - icons inherit via currentColor
  color: var(--t-color-neutral-900);

  .dot {
    width: var(--t-spacing-sm);
    height: var(--t-spacing-sm);
    border-radius: var(--t-border-radius-half-rounded);
  }

  .icon {
    flex-shrink: 0;
  }

  .label {
    color: var(--t-color-neutral-900);
    font: var(--t-typography-label-md-font);
  }

  &.small {
    .label {
      font: var(--t-typography-label-sm-font);
    }
  }

  &.orange {
    background-color: var(--t-color-orange-200);

    .dot {
      background-color: var(--t-color-orange-400);
    }
  }

  &.green {
    background-color: var(--t-color-green-200);

    .dot {
      background-color: var(--t-color-green-500);
    }
  }

  &.blue {
    background-color: #c5ddf2;

    .dot {
      background-color: #3387c4;
    }
  }

  &.violet {
    background-color: #e8ddf1;

    .dot {
      background-color: #8857a2;
    }
  }

  &.yellow {
    background-color: var(--t-color-yellow-200);

    .dot {
      background-color: var(--t-color-yellow-400);
    }
  }

  &.red {
    background-color: var(--t-color-red-200);

    .dot {
      background-color: var(--t-color-red-400);
    }
  }

  &.grey {
    background-color: var(--t-color-neutral-200);

    .dot {
      background-color: var(--t-color-neutral-500);
    }
  }

  &.grey-dark {
    background-color: var(--t-text-color-tertiary);

    .dot {
      background-color: var(--t-color-neutral-400);
    }

    .label {
      color: var(--t-color-neutral-100);
    }
  }

  &.pink-dust {
    background-color: #eedce5;

    .dot {
      background-color: #cd5b81;
    }
  }
}
</style>
