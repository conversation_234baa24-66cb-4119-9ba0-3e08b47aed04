import { AppRouteNames } from './app-routes.enum';

interface NavigationRoute {
  appRoute: AppRouteNames;
  title: string;
  available?: () => boolean;
}

export const mainNavigationConfig: NavigationRoute[] = [
  {
    appRoute: AppRouteNames.DEV_TOOLS,
    title: 'Dev Tools',
    available: () => {
      return process.env.ENV !== 'production' && localStorage.getItem('dev-tools-on') === 'true';
    },
  },
  {
    appRoute: AppRouteNames.DASHBOARD,
    title: 'Home',
  },
  {
    appRoute: AppRouteNames.INVESTOR_PORTFOLIO,
    title: 'Portfolio',
  },
  {
    appRoute: AppRouteNames.INVESTOR_CALENDAR,
    title: 'Calendar',
  },
  {
    appRoute: AppRouteNames.INVESTOR_SHOWINGS,
    title: 'Showings',
  },
].filter((route) => {
  if (route?.available) {
    return route.available();
  }

  return true;
});
