<template>
  <View gap="md" direction="column">
    <template v-for="item in items">
      <template v-if="item.type === ActionNeededType.IntelligentEscalation">
        <IntelligentEscalationCard :key="item.item.id" :question="item.item" @click="showModal(item.item)" />
      </template>
      <template v-else-if="item.type === ActionNeededType.ShowingRequest">
        <ShowingCard :key="item.item.id" :showing="item.item" />
      </template>
      <template v-else-if="item.type === ActionNeededType.Application">
        <ApplicationBundleCard :key="item.item.id" :applicationBundle="item.item" />
      </template>
    </template>
  </View>

  <IntelligentEscalationModal
    v-if="selectedEscalation"
    :escalation="selectedEscalation"
    @closed="resetSelectedEscalation"
    ref="intelligentEscalationModalRef"
  />
</template>

<script setup lang="ts">
import { nextTick, ref } from 'vue';

import { View } from '@tallo/design-system';
import { ApplicationBundleCard } from '@tallo/investor/applications';
import {
  IntelligentEscalationCard,
  IntelligentEscalationDto,
  IntelligentEscalationModal,
} from '@tallo/investor/intelligent-escalations';
import { ShowingCard } from '@tallo/investor/showing';

import { ActionNeeded, ActionNeededType } from '../interfaces/actions-needed-item.interface';

defineProps<{ items: ActionNeeded[] }>();

const intelligentEscalationModalRef = ref<InstanceType<typeof IntelligentEscalationModal>>();
const selectedEscalation = ref<IntelligentEscalationDto>();

function resetSelectedEscalation() {
  selectedEscalation.value = undefined;
}

function showModal(escalation: IntelligentEscalationDto) {
  selectedEscalation.value = escalation;

  nextTick(() => {
    intelligentEscalationModalRef.value?.open();
  });
}
</script>

<style scoped></style>
