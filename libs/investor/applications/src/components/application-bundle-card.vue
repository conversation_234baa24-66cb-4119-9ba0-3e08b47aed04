<template>
  <Card variant="secondary" size="medium">
    <View gap="md" direction="column">
      <Text variant="label-medium" color="secondary">Application for review</Text>
      <View gap="sm" direction="column">
        <View gap="sm">
          <PropertyPhotoPreview :image-url="property.coverImage" size="sm" />

          <View justify="center" direction="column">
            <Text variant="h5">{{ property.displayName }}</Text>
            <Text variant="label-medium" color="secondary">{{ userName }}</Text>
          </View>
        </View>

        <View gap="md" justify="space-between" align="end">
          <Text variant="body-small" color="secondary">Last update {{ lastUpdateTime }}</Text>
          <Button
            v-if="isReadyForReview"
            variant="primary"
            size="medium"
            :to="renterApplicationReviewRoute(property.id, renterId, applicationBundle.id)"
          >
            Review
          </Button>
          <Button v-else variant="secondary" size="medium" :to="renterProfileRoute(property.id, renterId)">
            See details
          </Button>
        </View>
      </View>
    </View>
  </Card>
</template>

<script setup lang="ts">
import { useTimeAgo } from '@vueuse/core';
import { computed } from 'vue';

import { ApplicationBundleStatus } from '@tallo/applications';
import { Button, Card, Text, View } from '@tallo/design-system';
import { renterApplicationReviewRoute, renterProfileRoute } from '@tallo/investor/navigation';
import { PropertyPhotoPreview } from '@tallo/property';

import { ApplicationBundleDto } from '../interfaces/application-bundle.interfaces';

const props = defineProps<{ applicationBundle: ApplicationBundleDto }>();

const applicationBundle = computed(() => props.applicationBundle);
const property = computed(() => applicationBundle.value.property);
const userName = computed(() => applicationBundle.value.primaryApplicant.user.name);
const renterId = computed(() => applicationBundle.value.primaryApplicant.id);
const isReadyForReview = computed(() => applicationBundle.value.status === ApplicationBundleStatus.SUBMITTED);
const lastUpdateTime = useTimeAgo(applicationBundle.value.updatedAt);
</script>

<style scoped lang="scss"></style>
