<template>
  <Modal position="end" ref="modalDialogRef" @after-close="emit('closed')">
    <template #header>Intelligent escalation</template>
    <template #body>
      <View direction="column" gap="lg">
        <View direction="column" gap="sm">
          <Text variant="h3">Answer question</Text>
          <Text variant="body-medium" color="tertiary">
            Once you submit your response, we’ll automatically share it with all renters who asked it. We’ll also save
            your response so you won’t have to answer the same question again
          </Text>
        </View>
        <Card variant="secondary" size="medium" class="info-card">
          <View direction="column" gap="md">
            <View direction="column" gap="sm">
              <Text variant="h5">{{ propertyQuestion.title }}</Text>
              <Text variant="body-medium">{{ propertyQuestion.questionText }}</Text>
            </View>

            <Divider />

            <View align="center" justify="space-between">
              <AvatarsRowList :renters="propertyQuestion.renters" :property-id="propertyQuestion.property.id" />
              <Text variant="body-small" color="tertiary">
                {{ useDateFormat(propertyQuestion.createdAt, 'MMM D') }}
              </Text>
            </View>

            <Text variant="body-medium" color="secondary">{{ propertyQuestion.property.displayName }}</Text>
          </View>
        </Card>

        <Textarea placeholder="Type your answer here" rows="4" v-model="form.fields.answer" />
      </View>
    </template>
    <template #footer>
      <Button variant="ghost" @click="close">Cancel</Button>
      <View gap="md">
        <Button variant="outline" @click="ignore" :loading="isSubmittingIgnoreEscalation">Ignore</Button>
        <Button
          :disabled="form.validation.$invalid || isSubmittingIgnoreEscalation"
          :loading="form.submitting"
          @click="submit"
        >
          Send
        </Button>
      </View>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { required } from '@vuelidate/validators';
import { useDateFormat } from '@vueuse/core';
import { computed, ref } from 'vue';

import { Button, Card, Divider, Modal, Textarea, View, Text, useForm } from '@tallo/design-system';
import { AvatarsRowList } from '@tallo/investor/renter';

import { IntelligentEscalationDto } from '../interfaces/intelligent-escalation.interface';
import { useAnswerIntelligentEscalation } from '../mutations/answer-intelligent-escalation.mutation';
import { useIgnoreIntelligentEscalation } from '../mutations/ignore-intelligent-escalation.mutation';

const emit = defineEmits(['closed']);
const props = defineProps<{ propertyQuestion: IntelligentEscalationDto }>();

const answerIntelligentEscalation = useAnswerIntelligentEscalation();
const ignoreIntelligentEscalation = useIgnoreIntelligentEscalation();
const isSubmittingIgnoreEscalation = computed(() => ignoreIntelligentEscalation.isLoading.value);
const modalDialogRef = ref<InstanceType<typeof Modal> | null>(null);
const form = useForm<{ answer: string }>({ answer: '' }, { answer: { required } });

function open() {
  modalDialogRef.value?.show();
}

function close() {
  modalDialogRef.value?.close();
}

async function submit() {
  await form.submit(async () => {
    await answerIntelligentEscalation.mutateAsync({
      propertyId: props.propertyQuestion.property.id,
      escalationId: props.propertyQuestion.id,
      answer: form.fields.answer,
    });

    close();
  });
}

async function ignore() {
  await ignoreIntelligentEscalation.mutateAsync({
    propertyId: props.propertyQuestion.property.id,
    escalationId: props.propertyQuestion.id,
  });

  close();
}

defineExpose({
  open,
  close,
  modalDialogRef,
});
</script>
