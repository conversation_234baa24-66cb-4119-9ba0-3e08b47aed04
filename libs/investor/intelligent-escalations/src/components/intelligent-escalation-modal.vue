<template>
  <PropertyQuestionModal
    v-if="escalationType === IntelligentEscalationType.PROPERTY_QUESTION"
    :property-question="escalation"
    @closed="$emit('closed')"
    ref="questionModalRef"
  />

  <SelfShowingInstructionsModal
    v-if="escalationType === IntelligentEscalationType.SELF_SHOWING_INSTRUCTIONS_REQUEST"
    :escalation="escalation"
    :renter="escalation?.renters?.[0]"
    @closed="$emit('closed')"
    ref="selfShowingModalRef"
  />

  <PhoneCallRequestModal
    v-if="escalationType === IntelligentEscalationType.PHONE_CALL_REQUEST"
    :escalation="escalation"
    @closed="$emit('closed')"
    ref="phoneCallModalRef"
  />
</template>

<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';

import { IntelligentEscalationType } from '../enums/intelligent-escalation-type.enum';
import { IntelligentEscalationDto } from '../interfaces/intelligent-escalation.interface';
import PhoneCallRequestModal from './phone-call-request-modal.vue';
import PropertyQuestionModal from './property-question-modal.vue';
import SelfShowingInstructionsModal from './self-showing-instructions-modal.vue';

const emit = defineEmits(['closed']);
const props = defineProps<{ escalation: IntelligentEscalationDto }>();
const questionModalRef = ref<InstanceType<typeof PropertyQuestionModal> | null>(null);
const selfShowingModalRef = ref<InstanceType<typeof SelfShowingInstructionsModal> | null>(null);
const phoneCallModalRef = ref<InstanceType<typeof PhoneCallRequestModal> | null>(null);

const escalation = computed(() => props.escalation);
const escalationType = computed(() => escalation.value.type);

function open() {
  switch (escalationType.value) {
    case IntelligentEscalationType.PROPERTY_QUESTION:
      questionModalRef.value!.open();
      break;

    case IntelligentEscalationType.SELF_SHOWING_INSTRUCTIONS_REQUEST:
      selfShowingModalRef.value!.open();
      break;

    case IntelligentEscalationType.PHONE_CALL_REQUEST:
      phoneCallModalRef.value!.open();
      break;
  }
}

defineExpose({ open });
</script>

<style scoped></style>
