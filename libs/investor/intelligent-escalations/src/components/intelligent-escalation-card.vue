<template>
  <Card variant="secondary" size="medium" class="property-question-card">
    <View direction="column" gap="md">
      <View gap="md" justify="space-between">
        <Text variant="label-medium" color="secondary">Intelligent escalation</Text>
        <Text variant="body-small" color="tertiary">
          {{ useDateFormat(question.createdAt, 'MMM D') }}
        </Text>
      </View>

      <View gap="sm" justify="space-between">
        <div>
          <Text variant="h4">{{ question.title }}</Text>
          <Text variant="body-medium" color="secondary">{{ question.property.displayName }}</Text>
        </div>
        <Badge :label="badgeConfig.label" :variant="badgeConfig.color" :icon="badgeConfig.icon" size="small"></Badge>
      </View>

      <Card variant="tertiary" size="small">
        <Text variant="body-medium">{{ question.questionText }}</Text>
      </Card>
    </View>
  </Card>
</template>

<script setup lang="ts">
import { useDateFormat } from '@vueuse/core';
import { computed } from 'vue';

import { Badge, BadgeVariant, Card, IconName, Text, View } from '@tallo/design-system';

import { IntelligentEscalationType } from '../enums/intelligent-escalation-type.enum';
import { IntelligentEscalationDto } from '../interfaces/intelligent-escalation.interface';

const props = defineProps<{ question: IntelligentEscalationDto }>();

const badgeConfigMap = new Map<IntelligentEscalationType, { label: string; color: BadgeVariant; icon: IconName }>([
  [
    IntelligentEscalationType.PROPERTY_QUESTION,
    {
      label: 'Property',
      color: 'pink-dust',
      icon: 'home-03',
    },
  ],
  [
    IntelligentEscalationType.PHONE_CALL_REQUEST,
    {
      label: 'Call request',
      color: 'pink-dust',
      icon: 'phone',
    },
  ],
  [
    IntelligentEscalationType.SELF_SHOWING_INSTRUCTIONS_REQUEST,
    {
      label: 'Self showing',
      color: 'pink-dust',
      icon: 'key',
    },
  ],
]);
const badgeConfig = computed(() => badgeConfigMap.get(props.question.type)!);
</script>

<style scoped lang="scss">
.property-question-card {
  width: 100%;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.question-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
