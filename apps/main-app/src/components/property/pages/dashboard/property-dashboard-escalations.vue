<template>
  <View gap="lg" direction="column" id="pd-escalations">
    <View justify="space-between">
      <Text variant="h2" ref="escalationsSection">
        Intelligent Escalations
        <span v-if="hasEscalations">({{ escalationsAmount }})</span>
      </Text>

      <ScrollableHorizontallyControls :scrollable-container="escalationsScrollableContainerRef" />
    </View>

    <View direction="column" gap="md">
      <Text variant="body-medium" color="tertiary" class="hint" v-if="hasEscalations">
        For questions don't have the best answer for, we reach out for your input. We will then reply and enrich your
        profile to prevent this question in the future.
      </Text>

      <Card variant="outline" size="medium" class="hint" v-else>
        <template #header>
          <Text variant="h4">What are Intelligent Escalations?</Text>
        </template>

        <Text variant="body-medium" color="secondary">
          For questions don't have the best answer for, we reach out for your input. We will then reply and enrich your
          profile to prevent this question in the future.
        </Text>
      </Card>

      <ScrollableHorizontallyContainer ref="escalationsScrollableContainerRef">
        <View class="card-items" gap="sm" padding-bottom="md" v-if="hasEscalations">
          <IntelligentEscalationCard
            class="card-item"
            v-for="question in escalations"
            :key="question.id"
            :question="question"
            @click="showModal(question)"
          />
        </View>
      </ScrollableHorizontallyContainer>
    </View>

    <IntelligentEscalationModal
      v-if="selectedEscalation"
      :escalation="selectedEscalation"
      @closed="resetSelectedEscalation"
      ref="intelligentEscalationModalRef"
    />
  </View>
</template>

<script setup lang="ts">
import { until } from '@vueuse/core';
import { computed, ref, nextTick, onMounted } from 'vue';
import { useRoute } from 'vue-router';

import {
  Card,
  ScrollableHorizontallyContainer,
  ScrollableHorizontallyControls,
  Text,
  View,
} from '@tallo/design-system';
import {
  IntelligentEscalationCard,
  IntelligentEscalationDto,
  IntelligentEscalationModal,
  usePropertyIntelligentEscalationsQuery,
} from '@tallo/investor/intelligent-escalations';

const route = useRoute();
const { data, status } = usePropertyIntelligentEscalationsQuery();
const escalationsScrollableContainerRef = ref();
const intelligentEscalationModalRef = ref<InstanceType<typeof IntelligentEscalationModal> | null>(null);
const selectedEscalation = ref<IntelligentEscalationDto>();

const escalations = computed(() => data.value || []);
const isLoaded = computed(() => status.value === 'success');
const escalationsAmount = computed(() => escalations.value?.length);
const hasEscalations = computed(() => escalationsAmount.value > 0);

onMounted(() => {
  openQuestionBasedOnQueryParam();
});

function showModal(escalation: IntelligentEscalationDto) {
  selectedEscalation.value = escalation;

  nextTick(() => {
    intelligentEscalationModalRef.value?.open();
  });
}

function resetSelectedEscalation() {
  selectedEscalation.value = undefined;
}

async function openQuestionBasedOnQueryParam() {
  await until(isLoaded).toMatch((value) => !!value);

  const escalationsId = route.query.questionId;

  if (!escalationsId) {
    return;
  }

  const escalation = escalations.value.find(({ id }) => id === escalationsId);

  if (escalation) {
    showModal(escalation);
  }
}
</script>

<style scoped lang="scss">
@import 'styles/mixins/layout.mixins';

.card-items {
  .card-item {
    width: 28.3333rem;
    flex-shrink: 0;
  }

  @include mobile {
    flex-direction: column;

    .card-item {
      width: 100%;
    }
  }
}

.hint {
  max-width: 28.3333rem;
}
</style>
