<template>
  <View gap="sm" direction="column">
    <View gap="md" direction="row" justify="space-between">
      <Text variant="h3">Activity feed</Text>
      <Text
        as="button"
        variant="body-large"
        color="secondary"
        v-if="hasMoreDataThanLimit"
        class="see-all"
        @click="openModal"
      >
        See all
      </Text>
    </View>

    <View v-if="isPending" justify="center" align="center" padding="lg">
      <LoadingIndicator />
    </View>

    <View v-else-if="data.length === 0" justify="center" align="center" padding="lg" direction="column" gap="sm">
      <Text variant="body-medium" color="tertiary" align="center">No recent activity</Text>
      <Text variant="body-small" color="tertiary" align="center">
        Activity from your properties and applications will appear here
      </Text>
    </View>

    <ActivityFeedList v-else :items="items" />

    <ActivityFeedModal ref="activityFeedModalRef" />
  </View>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import { LoadingIndicator, Text, View } from '@tallo/design-system';
import { useActivityFeedQuery, ActivityFeedModal, ActivityFeedList } from '@tallo/investor/activity-feed';

const activityFeedQuery = useActivityFeedQuery();
const data = computed(() => activityFeedQuery.data?.value || []);
const isPending = computed(() => activityFeedQuery.isPending.value);
const limit = 8;
const items = computed(() => data.value.slice(0, limit));
const hasMoreDataThanLimit = computed(() => data.value.length > limit);
const activityFeedModalRef = ref<InstanceType<typeof ActivityFeedModal>>();

function openModal() {
  activityFeedModalRef.value?.open();
}
</script>

<style scoped lang="scss">
.see-all {
  appearance: none;
  border: none;
  background: none;

  &:hover {
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
