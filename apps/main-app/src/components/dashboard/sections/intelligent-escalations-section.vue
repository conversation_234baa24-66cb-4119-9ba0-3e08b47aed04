<template>
  <View v-if="isLoading" justify="center" align="center" padding="xl">
    <LoadingIndicator />
  </View>

  <View
    v-else-if="sortedEscalations.length === 0"
    justify="center"
    align="center"
    padding="xl"
    direction="column"
    gap="md"
  >
    <Text variant="h3" align="center">No escalations to review</Text>
    <Text variant="body-large" color="tertiary" align="center">
      When renters have questions or need assistance, intelligent escalations will appear here for your attention.
    </Text>
  </View>

  <div v-else class="intelligent-escalations">
    <IntelligentEscalationCard
      v-for="escalation in sortedEscalations"
      :key="escalation.id"
      :question="escalation"
      @click="showModal(escalation)"
    />
  </div>

  <IntelligentEscalationModal
    v-if="selectedEscalation"
    :escalation="selectedEscalation"
    @closed="resetSelectedEscalation"
    ref="intelligentEscalationModalRef"
  />
</template>

<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';

import { LoadingIndicator, Text, View } from '@tallo/design-system';
import {
  IntelligentEscalationCard,
  IntelligentEscalationDto,
  IntelligentEscalationModal,
} from '@tallo/investor/intelligent-escalations';

interface Props {
  isLoading: boolean;
  escalations: IntelligentEscalationDto[];
}

const props = defineProps<Props>();
const intelligentEscalationModalRef = ref<InstanceType<typeof IntelligentEscalationModal>>();
const selectedEscalation = ref<IntelligentEscalationDto>();

const sortedEscalations = computed(() => {
  return props.escalations.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
});

function showModal(escalation: IntelligentEscalationDto) {
  selectedEscalation.value = escalation;

  nextTick(() => {
    intelligentEscalationModalRef.value?.open();
  });
}

function resetSelectedEscalation() {
  selectedEscalation.value = undefined;
}
</script>

<style scoped lang="scss">
.intelligent-escalations {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
  gap: var(--t-spacing-md);
}
</style>
