<template>
  <Card variant="secondary">
    <template #header>
      <Text variant="h1">Performance</Text>
      <LoadingIndicator v-if="isLoading" />
    </template>

    <View gap="lg" direction="column">
      <Statistics :data="statisticsData" v-model="selectedConfigName" />
      <div class="filters">
        <TopBarFilters :filters="filters" :selectedFilter="selectedFilter" @selected="selectedFilter = $event" />
      </div>
      <div class="chart">
        <Chart v-if="timeSeriesData" :config-name="selectedConfigName" :time-series-data="timeSeriesData" />
        <View v-else justify="center" align="center" class="loading-indicator">
          <LoadingIndicator />
        </View>
      </div>
    </View>
  </Card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import { Card, LoadingIndicator, Text, TopBarFilters, View } from '@tallo/design-system';
import {
  PortfolioStatisticsFilter,
  usePortfolioStatisticsQuery,
  usePortfolioTimeSeriesQuery,
} from '@tallo/investor/portfolio-statistics';

import Chart from './performance/chart.vue';
import { StatisticsConfigName } from './performance/statistics-config';
import Statistics from './performance/statistics.vue';

const filters = Object.values(PortfolioStatisticsFilter);
const selectedConfigName = ref(StatisticsConfigName.LEADS);
const selectedFilter = ref(filters[0]);
const timeSeriesQuery = usePortfolioTimeSeriesQuery(selectedFilter);
const timeSeriesData = computed(() => timeSeriesQuery.data.value || undefined);
const statisticsQuery = usePortfolioStatisticsQuery(selectedFilter);
const statisticsData = computed(() => statisticsQuery.data.value || undefined);
const isLoading = computed(() => timeSeriesQuery.isPending.value || statisticsQuery.isPending.value);
</script>

<style scoped lang="scss">
.chart {
  width: 100%;
  height: 14rem;
}

.loading-indicator {
  width: 100%;
  height: 100%;
}
</style>
