<template>
  <PageContainer height="full" class="dashboard-layout">
    <View gap="lg" direction="column" :grow="1" class="main-content">
      <DashboardPerformance />
      <DashboardSections />
    </View>
    <View gap="lg" direction="column" :shrink="0" class="aside">
      <DashboardActionsNeeded :limit="2" />
      <DashboardActivityFeed />
    </View>
  </PageContainer>
</template>

<script setup lang="ts">
import { PageContainer, View } from '@tallo/design-system';

import DashboardActionsNeeded from './dashboard-actions-needed.vue';
import DashboardActivityFeed from './dashboard-activity-feed.vue';
import DashboardPerformance from './dashboard-performance.vue';
import DashboardSections from './dashboard-sections.vue';
</script>

<style scoped lang="scss">
.dashboard-layout {
  --gap: var(--t-spacing-md);
  --aside-width: 25rem;

  display: flex;
  gap: var(--gap);

  .main-content {
    display: flex;
    gap: var(--t-spacing-md);
    max-width: calc(100% - var(--aside-width) - var(--gap));
    width: calc(100% - var(--aside-width) - var(--gap));
  }

  .aside {
    width: var(--aside-width);
  }
}
</style>
